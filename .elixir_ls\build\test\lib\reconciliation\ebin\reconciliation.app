{application,reconciliation,
    [{compile_env,
         [{reconciliation,['Elixir.ReconciliationWeb.Gettext'],error},
          {reconciliation,[dev_routes],error}]},
     {applications,
         [kernel,stdlib,elixir,logger,runtime_tools,pbkdf2_elixir,phoenix,
          phoenix_ecto,ecto_sql,postgrex,phoenix_html,phoenix_live_view,floki,
          phoenix_live_dashboard,swoosh,finch,telemetry_metrics,
          telemetry_poller,gettext,jason,dns_cluster,bandit,xlsxir,nimble_csv,
          timex]},
     {description,"reconciliation"},
     {modules,
         ['Elixir.Inspect.Reconciliation.Accounts.User',
          'Elixir.Mix.Tasks.Reconciliation.Reprocess','Elixir.Reconciliation',
          'Elixir.Reconciliation.Accounts',
          'Elixir.Reconciliation.Accounts.User',
          'Elixir.Reconciliation.Accounts.UserNotifier',
          'Elixir.Reconciliation.Accounts.UserToken',
          'Elixir.Reconciliation.AccountsFixtures',
          'Elixir.Reconciliation.Application',
          'Elixir.Reconciliation.DataCase','Elixir.Reconciliation.Mailer',
          'Elixir.Reconciliation.Reconciliation',
          'Elixir.Reconciliation.ReconciliationRun',
          'Elixir.Reconciliation.ReconciliationSettings',
          'Elixir.Reconciliation.Repo',
          'Elixir.Reconciliation.Services.ExcelParser',
          'Elixir.Reconciliation.Services.MatchingEngine',
          'Elixir.Reconciliation.Tasks.ReprocessMatches',
          'Elixir.Reconciliation.Transaction',
          'Elixir.Reconciliation.TransactionMatch',
          'Elixir.Reconciliation.UploadedFile','Elixir.ReconciliationWeb',
          'Elixir.ReconciliationWeb.ConnCase',
          'Elixir.ReconciliationWeb.CoreComponents',
          'Elixir.ReconciliationWeb.DashboardLive',
          'Elixir.ReconciliationWeb.DataInspectionLive',
          'Elixir.ReconciliationWeb.Endpoint',
          'Elixir.ReconciliationWeb.ErrorHTML',
          'Elixir.ReconciliationWeb.ErrorJSON',
          'Elixir.ReconciliationWeb.Gettext',
          'Elixir.ReconciliationWeb.Layouts',
          'Elixir.ReconciliationWeb.NavbarComponent',
          'Elixir.ReconciliationWeb.PageController',
          'Elixir.ReconciliationWeb.PageHTML',
          'Elixir.ReconciliationWeb.ReconciliationResultsLive',
          'Elixir.ReconciliationWeb.ReconciliationUploadLive',
          'Elixir.ReconciliationWeb.ReportsLive',
          'Elixir.ReconciliationWeb.Router',
          'Elixir.ReconciliationWeb.SettingsLive',
          'Elixir.ReconciliationWeb.SidebarComponent',
          'Elixir.ReconciliationWeb.Telemetry',
          'Elixir.ReconciliationWeb.TransactionDebugLive',
          'Elixir.ReconciliationWeb.TransactionsLive',
          'Elixir.ReconciliationWeb.UserAuth',
          'Elixir.ReconciliationWeb.UserConfirmationInstructionsLive',
          'Elixir.ReconciliationWeb.UserConfirmationLive',
          'Elixir.ReconciliationWeb.UserForgotPasswordLive',
          'Elixir.ReconciliationWeb.UserLoginLive',
          'Elixir.ReconciliationWeb.UserRegistrationLive',
          'Elixir.ReconciliationWeb.UserResetPasswordLive',
          'Elixir.ReconciliationWeb.UserSessionController',
          'Elixir.ReconciliationWeb.UserSettingsLive']},
     {registered,[]},
     {vsn,"0.1.0"},
     {mod,{'Elixir.Reconciliation.Application',[]}}]}.

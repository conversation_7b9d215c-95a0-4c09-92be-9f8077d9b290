defmodule Reconciliation.TransactionMatch do
  use Ecto.Schema
  import Ecto.Changeset

  alias Reconciliation.{ReconciliationRun, Transaction}

  schema "transaction_matches" do
    field :match_type, :string
    field :confidence_score, :decimal
    field :amount_difference, :decimal, default: Decimal.new("0")
    field :date_difference_days, :integer, default: 0
    field :matching_criteria, {:array, :string}
    field :notes, :string
    field :verified_by_user, :boolean, default: false
    field :verified_at, :utc_datetime

    belongs_to :reconciliation_run, ReconciliationRun
    belongs_to :transaction_a, Transaction
    belongs_to :transaction_b, Transaction

    timestamps(type: :utc_datetime)
  end

  @doc false
  def changeset(transaction_match, attrs) do
    transaction_match
    |> cast(attrs, [
      :match_type, :confidence_score, :amount_difference, :date_difference_days,
      :matching_criteria, :notes, :verified_by_user, :verified_at,
      :reconciliation_run_id, :transaction_a_id, :transaction_b_id
    ])
    |> validate_required([
      :match_type, :confidence_score, :reconciliation_run_id,
      :transaction_a_id, :transaction_b_id
    ])
    |> validate_inclusion(:match_type, ["exact", "fuzzy", "manual", "partial"])
    |> validate_number(:confidence_score, greater_than_or_equal_to: 0, less_than_or_equal_to: 100)
    |> validate_number(:date_difference_days, greater_than_or_equal_to: 0)
    |> foreign_key_constraint(:reconciliation_run_id)
    |> foreign_key_constraint(:transaction_a_id)
    |> foreign_key_constraint(:transaction_b_id)
    |> unique_constraint([:transaction_a_id, :transaction_b_id])
  end

  @doc """
  Creates a changeset for user verification
  """
  def verification_changeset(transaction_match, verified \\ true) do
    attrs = %{
      verified_by_user: verified,
      verified_at: if(verified, do: DateTime.utc_now(), else: nil)
    }

    transaction_match
    |> cast(attrs, [:verified_by_user, :verified_at])
  end

  @doc """
  Creates a changeset for adding notes
  """
  def notes_changeset(transaction_match, notes) do
    transaction_match
    |> cast(%{notes: notes}, [:notes])
    |> validate_length(:notes, max: 1000)
  end

  @doc """
  Creates an exact match between two transactions
  """
  def create_exact_match(transaction_a, transaction_b, reconciliation_run_id) do
    %__MODULE__{}
    |> changeset(%{
      match_type: "exact",
      confidence_score: Decimal.new("100"),
      amount_difference: calculate_amount_difference(transaction_a, transaction_b),
      date_difference_days: calculate_date_difference(transaction_a, transaction_b),
      matching_criteria: ["amount", "date", "reference"],
      reconciliation_run_id: reconciliation_run_id,
      transaction_a_id: transaction_a.id,
      transaction_b_id: transaction_b.id
    })
  end

  @doc """
  Creates a fuzzy match between two transactions
  """
  def create_fuzzy_match(transaction_a, transaction_b, reconciliation_run_id, confidence, criteria) do
    %__MODULE__{}
    |> changeset(%{
      match_type: "fuzzy",
      confidence_score: confidence,
      amount_difference: calculate_amount_difference(transaction_a, transaction_b),
      date_difference_days: calculate_date_difference(transaction_a, transaction_b),
      matching_criteria: criteria,
      reconciliation_run_id: reconciliation_run_id,
      transaction_a_id: transaction_a.id,
      transaction_b_id: transaction_b.id
    })
  end

  @doc """
  Creates a manual match between two transactions
  """
  def create_manual_match(transaction_a, transaction_b, reconciliation_run_id, notes \\ nil) do
    %__MODULE__{}
    |> changeset(%{
      match_type: "manual",
      confidence_score: Decimal.new("100"),
      amount_difference: calculate_amount_difference(transaction_a, transaction_b),
      date_difference_days: calculate_date_difference(transaction_a, transaction_b),
      matching_criteria: ["manual"],
      notes: notes,
      verified_by_user: true,
      verified_at: DateTime.utc_now(),
      reconciliation_run_id: reconciliation_run_id,
      transaction_a_id: transaction_a.id,
      transaction_b_id: transaction_b.id
    })
  end

  @doc """
  Checks if the match is exact
  """
  def exact?(%__MODULE__{match_type: "exact"}), do: true
  def exact?(_), do: false

  @doc """
  Checks if the match is fuzzy
  """
  def fuzzy?(%__MODULE__{match_type: "fuzzy"}), do: true
  def fuzzy?(_), do: false

  @doc """
  Checks if the match is manual
  """
  def manual?(%__MODULE__{match_type: "manual"}), do: true
  def manual?(_), do: false

  @doc """
  Checks if the match has been verified by user
  """
  def verified?(%__MODULE__{verified_by_user: true}), do: true
  def verified?(_), do: false

  @doc """
  Checks if the match has high confidence (>= 90%)
  """
  def high_confidence?(%__MODULE__{confidence_score: score}) do
    Decimal.compare(score, Decimal.new("90")) != :lt
  end

  @doc """
  Checks if the match has medium confidence (70-89%)
  """
  def medium_confidence?(%__MODULE__{confidence_score: score}) do
    Decimal.compare(score, Decimal.new("70")) != :lt and
    Decimal.compare(score, Decimal.new("90")) == :lt
  end

  @doc """
  Checks if the match has low confidence (< 70%)
  """
  def low_confidence?(%__MODULE__{confidence_score: score}) do
    Decimal.compare(score, Decimal.new("70")) == :lt
  end

  @doc """
  Returns confidence level as atom
  """
  def confidence_level(%__MODULE__{} = match) do
    cond do
      high_confidence?(match) -> :high
      medium_confidence?(match) -> :medium
      true -> :low
    end
  end

  @doc """
  Returns confidence level color for UI
  """
  def confidence_color(%__MODULE__{} = match) do
    case confidence_level(match) do
      :high -> "green"
      :medium -> "yellow"
      :low -> "red"
    end
  end

  @doc """
  Calculates amount difference between two transactions.
  For reconciliation purposes, we compare absolute amounts since the same
  economic transaction should have zero difference regardless of how amounts
  are stored (positive vs negative).
  """
  def calculate_amount_difference(%Transaction{amount: amount_a}, %Transaction{amount: amount_b}) do
    # Compare absolute amounts to handle cases where the same economic transaction
    # might be stored with different signs (e.g., debit as +1500 vs -1500)
    abs_a = Decimal.abs(amount_a)
    abs_b = Decimal.abs(amount_b)
    Decimal.sub(abs_a, abs_b) |> Decimal.abs()
  end

  @doc """
  Calculates date difference between two transactions
  """
  def calculate_date_difference(%Transaction{transaction_date: nil}, %Transaction{}), do: 0
  def calculate_date_difference(%Transaction{}, %Transaction{transaction_date: nil}), do: 0
  def calculate_date_difference(%Transaction{transaction_date: date_a}, %Transaction{transaction_date: date_b}) do
    Date.diff(date_a, date_b) |> abs()
  end

  @doc """
  Returns formatted amount difference
  """
  def formatted_amount_difference(%__MODULE__{amount_difference: diff}) do
    if Decimal.equal?(diff, Decimal.new("0")) do
      "No difference"
    else
      "$#{Decimal.to_string(diff, :normal)}"
    end
  end

  @doc """
  Returns formatted date difference
  """
  def formatted_date_difference(%__MODULE__{date_difference_days: 0}), do: "Same day"
  def formatted_date_difference(%__MODULE__{date_difference_days: 1}), do: "1 day"
  def formatted_date_difference(%__MODULE__{date_difference_days: days}), do: "#{days} days"
end

#!/usr/bin/env elixir

# Test script to verify the matching logic works correctly
# Run with: elixir test_matching.exs

Mix.install([
  {:decimal, "~> 2.0"}
])

defmodule TestTransaction do
  defstruct [
    :id, :transaction_date, :amount, :reference, :description,
    :transaction_type, :transaction_id, :account
  ]
end

defmodule TestMatching do
  alias TestTransaction
  def amount_match?(%TestTransaction{amount: amount1}, %TestTransaction{amount: amount2}, tolerance \\ Decimal.new("0.01")) do
    abs1 = Decimal.abs(amount1)
    abs2 = Decimal.abs(amount2)
    diff = Decimal.sub(abs1, abs2) |> Decimal.abs()
    Decimal.compare(diff, tolerance) != :gt
  end

  def date_match?(%TestTransaction{transaction_date: nil}, %TestTransaction{transaction_date: nil}, _), do: true
  def date_match?(%TestTransaction{transaction_date: nil}, %TestTransaction{}, _), do: false
  def date_match?(%TestTransaction{}, %TestTransaction{transaction_date: nil}, _), do: false
  def date_match?(%TestTransaction{transaction_date: date1}, %TestTransaction{transaction_date: date2}, tolerance_days) do
    diff = Date.diff(date1, date2) |> abs()
    diff <= tolerance_days
  end

  def reference_match?(%TestTransaction{reference: nil}, %TestTransaction{reference: nil}, _), do: true
  def reference_match?(%TestTransaction{reference: nil}, %TestTransaction{}, _), do: false
  def reference_match?(%TestTransaction{}, %TestTransaction{reference: nil}, _), do: false
  def reference_match?(%TestTransaction{reference: ref1}, %TestTransaction{reference: ref2}, _) when ref1 == ref2, do: true
  def reference_match?(_, _, _), do: false

  def transaction_type_match?(%TestTransaction{transaction_type: nil}, %TestTransaction{transaction_type: nil}), do: true
  def transaction_type_match?(%TestTransaction{transaction_type: nil}, %TestTransaction{}), do: false
  def transaction_type_match?(%TestTransaction{}, %TestTransaction{transaction_type: nil}), do: false
  def transaction_type_match?(%TestTransaction{transaction_type: type1}, %TestTransaction{transaction_type: type2}) when type1 == type2, do: true
  def transaction_type_match?(_, _), do: false

  def transaction_id_match?(%TestTransaction{transaction_id: nil}, %TestTransaction{transaction_id: nil}), do: true
  def transaction_id_match?(%TestTransaction{transaction_id: nil}, %TestTransaction{}), do: false
  def transaction_id_match?(%TestTransaction{}, %TestTransaction{transaction_id: nil}), do: false
  def transaction_id_match?(%TestTransaction{transaction_id: id1}, %TestTransaction{transaction_id: id2}) when id1 == id2, do: true
  def transaction_id_match?(_, _), do: false

  def account_match?(%TestTransaction{account: nil}, %TestTransaction{account: nil}), do: true
  def account_match?(%TestTransaction{account: nil}, %TestTransaction{}), do: false
  def account_match?(%TestTransaction{}, %TestTransaction{account: nil}), do: false
  def account_match?(%TestTransaction{account: acc1}, %TestTransaction{account: acc2}) when acc1 == acc2, do: true
  def account_match?(_, _), do: false

  # New improved exact match logic
  def is_exact_match?(a_txn, b_txn, amount_tolerance \\ Decimal.new("0.01"), date_tolerance_days \\ 3) do
    amount_match = amount_match?(a_txn, b_txn, amount_tolerance)
    date_match = date_match?(a_txn, b_txn, date_tolerance_days)

    # Core requirements: amount and date must match
    core_match = amount_match and date_match

    if not core_match do
      IO.puts("Core requirements failed (Amount: #{amount_match}, Date: #{date_match})")
      false
    else
      # Additional identifiers - at least one should match for exact match
      reference_match = reference_match?(a_txn, b_txn, nil)
      transaction_type_match = transaction_type_match?(a_txn, b_txn)
      transaction_id_match = transaction_id_match?(a_txn, b_txn)
      account_match = account_match?(a_txn, b_txn)

      # Count how many additional identifiers match
      additional_matches = [reference_match, transaction_type_match, transaction_id_match, account_match]
                          |> Enum.count(& &1)

      # For exact match, we need at least one additional identifier to match
      exact_match = additional_matches >= 1

      IO.puts("Amount: #{amount_match}, Date: #{date_match}, Ref: #{reference_match}, Type: #{transaction_type_match}, TxnID: #{transaction_id_match}, Account: #{account_match}, Additional matches: #{additional_matches} => #{exact_match}")

      exact_match
    end
  end

  # Old strict exact match logic for comparison
  def is_exact_match_old?(a_txn, b_txn, amount_tolerance \\ Decimal.new("0.01"), date_tolerance_days \\ 3) do
    amount_match = amount_match?(a_txn, b_txn, amount_tolerance)
    date_match = date_match?(a_txn, b_txn, date_tolerance_days)
    reference_match = reference_match?(a_txn, b_txn, nil)
    transaction_type_match = transaction_type_match?(a_txn, b_txn)
    transaction_id_match = transaction_id_match?(a_txn, b_txn)
    account_match = account_match?(a_txn, b_txn)

    exact_match = amount_match and date_match and reference_match and
                  transaction_type_match and transaction_id_match and account_match

    IO.puts("OLD LOGIC - Amount: #{amount_match}, Date: #{date_match}, Ref: #{reference_match}, Type: #{transaction_type_match}, TxnID: #{transaction_id_match}, Account: #{account_match} => #{exact_match}")

    exact_match
  end
end

# Test cases
IO.puts("=== Testing Transaction Matching Logic ===\n")

# Test case 1: Perfect match with all fields
IO.puts("Test 1: Perfect match with all fields")
txn_a1 = %TestTransaction{
  id: 1,
  transaction_date: ~D[2024-01-15],
  amount: Decimal.new("1500.00"),
  reference: "REF12345",
  description: "Payment to Supplier ABC",
  transaction_type: "debit",
  transaction_id: "TXN001",
  account: "ACC123"
}

txn_b1 = %TestTransaction{
  id: 2,
  transaction_date: ~D[2024-01-15],
  amount: Decimal.new("1500.00"),
  reference: "REF12345",
  description: "Payment to Supplier ABC",
  transaction_type: "debit",
  transaction_id: "TXN001",
  account: "ACC123"
}

IO.puts("NEW LOGIC:")
result1_new = TestMatching.is_exact_match?(txn_a1, txn_b1)
IO.puts("OLD LOGIC:")
result1_old = TestMatching.is_exact_match_old?(txn_a1, txn_b1)
IO.puts("Result - New: #{result1_new}, Old: #{result1_old}\n")

# Test case 2: Match with missing transaction_type and transaction_id (common scenario)
IO.puts("Test 2: Match with missing transaction_type and transaction_id")
txn_a2 = %TestTransaction{
  id: 3,
  transaction_date: ~D[2024-01-15],
  amount: Decimal.new("1500.00"),
  reference: "REF12345",
  description: "Payment to Supplier ABC",
  transaction_type: nil,
  transaction_id: nil,
  account: nil
}

txn_b2 = %TestTransaction{
  id: 4,
  transaction_date: ~D[2024-01-15],
  amount: Decimal.new("1500.00"),
  reference: "REF12345",
  description: "Payment to Supplier ABC",
  transaction_type: nil,
  transaction_id: nil,
  account: nil
}

IO.puts("NEW LOGIC:")
result2_new = TestMatching.is_exact_match?(txn_a2, txn_b2)
IO.puts("OLD LOGIC:")
result2_old = TestMatching.is_exact_match_old?(txn_a2, txn_b2)
IO.puts("Result - New: #{result2_new}, Old: #{result2_old}\n")

# Test case 3: Match with only amount and date (should fail both)
IO.puts("Test 3: Match with only amount and date (no additional identifiers)")
txn_a3 = %TestTransaction{
  id: 5,
  transaction_date: ~D[2024-01-15],
  amount: Decimal.new("1500.00"),
  reference: nil,
  description: "Payment to Supplier ABC",
  transaction_type: nil,
  transaction_id: nil,
  account: nil
}

txn_b3 = %TestTransaction{
  id: 6,
  transaction_date: ~D[2024-01-15],
  amount: Decimal.new("1500.00"),
  reference: nil,
  description: "Payment to Supplier ABC",
  transaction_type: nil,
  transaction_id: nil,
  account: nil
}

IO.puts("NEW LOGIC:")
result3_new = TestMatching.is_exact_match?(txn_a3, txn_b3)
IO.puts("OLD LOGIC:")
result3_old = TestMatching.is_exact_match_old?(txn_a3, txn_b3)
IO.puts("Result - New: #{result3_new}, Old: #{result3_old}\n")

IO.puts("=== Summary ===")
IO.puts("Test 1 (Perfect match): New=#{result1_new}, Old=#{result1_old}")
IO.puts("Test 2 (Realistic match): New=#{result2_new}, Old=#{result2_old}")
IO.puts("Test 3 (Amount+Date only): New=#{result3_new}, Old=#{result3_old}")
IO.puts("\nThe new logic should be more flexible and match realistic scenarios better!")

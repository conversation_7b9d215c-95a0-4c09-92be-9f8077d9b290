defmodule Reconciliation.Tasks.ReprocessMatches do
  @moduledoc """
  Task to reprocess existing reconciliation runs with improved matching logic.
  This can be used to fix reconciliation runs that were processed with the old,
  overly strict matching logic.
  """

  alias Reconciliation.{Repo, ReconciliationRun, Transaction, TransactionMatch}
  alias Reconciliation.Services.MatchingEngine
  import Ecto.Query
  require Logger

  @doc """
  Reprocesses all reconciliation runs that have low match rates.
  This is useful after improving the matching algorithm.
  """
  def reprocess_low_match_runs(match_rate_threshold \\ 50.0) do
    Logger.info("Finding reconciliation runs with match rate below #{match_rate_threshold}%")
    
    low_match_runs = from(r in ReconciliationRun,
      where: r.status == "completed" and r.match_rate < ^match_rate_threshold,
      order_by: [desc: r.inserted_at]
    )
    |> Repo.all()
    
    Logger.info("Found #{length(low_match_runs)} runs with low match rates")
    
    Enum.each(low_match_runs, fn run ->
      Logger.info("Reprocessing run #{run.id} (current match rate: #{Decimal.to_float(run.match_rate)}%)")
      reprocess_run(run.id)
    end)
    
    {:ok, length(low_match_runs)}
  end

  @doc """
  Reprocesses a specific reconciliation run with the current matching logic.
  """
  def reprocess_run(run_id) do
    Logger.info("Starting reprocessing of reconciliation run #{run_id}")
    
    run = Repo.get!(ReconciliationRun, run_id)
    
    # Clear existing matches
    clear_existing_matches(run_id)
    
    # Reset transaction match statuses
    reset_transaction_statuses(run_id)
    
    # Run matching engine again
    case MatchingEngine.match_transactions(run) do
      {:ok, matches} ->
        Logger.info("Reprocessing completed for run #{run_id}. Found #{length(matches)} matches")
        {:ok, matches}
      
      {:error, reason} ->
        Logger.error("Failed to reprocess run #{run_id}: #{inspect(reason)}")
        {:error, reason}
    end
  end

  @doc """
  Shows statistics about reconciliation runs that could benefit from reprocessing.
  """
  def analyze_runs do
    Logger.info("Analyzing reconciliation runs...")
    
    stats = from(r in ReconciliationRun,
      where: r.status == "completed",
      select: %{
        total_runs: count(r.id),
        avg_match_rate: avg(r.match_rate),
        min_match_rate: min(r.match_rate),
        max_match_rate: max(r.match_rate),
        low_match_runs: filter(count(r.id), r.match_rate < 50.0),
        zero_match_runs: filter(count(r.id), r.match_rate == 0.0)
      }
    )
    |> Repo.one()
    
    IO.puts("\n=== Reconciliation Run Analysis ===")
    IO.puts("Total completed runs: #{stats.total_runs}")
    IO.puts("Average match rate: #{if stats.avg_match_rate, do: "#{Float.round(Decimal.to_float(stats.avg_match_rate), 2)}%", else: "N/A"}")
    IO.puts("Min match rate: #{if stats.min_match_rate, do: "#{Decimal.to_float(stats.min_match_rate)}%", else: "N/A"}")
    IO.puts("Max match rate: #{if stats.max_match_rate, do: "#{Decimal.to_float(stats.max_match_rate)}%", else: "N/A"}")
    IO.puts("Runs with <50% match rate: #{stats.low_match_runs}")
    IO.puts("Runs with 0% match rate: #{stats.zero_match_runs}")
    
    if stats.zero_match_runs > 0 or stats.low_match_runs > 0 do
      IO.puts("\nRecommendation: Consider running reprocess_low_match_runs/0 to improve these results.")
    end
    
    stats
  end

  # Private functions

  defp clear_existing_matches(run_id) do
    Logger.info("Clearing existing matches for run #{run_id}")
    
    from(m in TransactionMatch, where: m.reconciliation_run_id == ^run_id)
    |> Repo.delete_all()
  end

  defp reset_transaction_statuses(run_id) do
    Logger.info("Resetting transaction match statuses for run #{run_id}")
    
    from(t in Transaction, 
      where: t.reconciliation_run_id == ^run_id,
      update: [set: [is_matched: false, match_confidence: nil]]
    )
    |> Repo.update_all([])
  end
end

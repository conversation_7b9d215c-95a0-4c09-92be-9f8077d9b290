defmodule Mix.Tasks.Reconciliation.Reprocess do
  @moduledoc """
  Mix task to reprocess reconciliation runs with improved matching logic.

  ## Examples

      # Analyze current reconciliation runs
      mix reconciliation.reprocess --analyze

      # Reprocess all runs with match rate below 50%
      mix reconciliation.reprocess --low-match

      # Reprocess a specific run
      mix reconciliation.reprocess --run-id 123

      # Reprocess runs with custom threshold
      mix reconciliation.reprocess --low-match --threshold 75
  """

  use Mix.Task
  alias Reconciliation.Tasks.ReprocessMatches

  @shortdoc "Reprocess reconciliation runs with improved matching logic"

  def run(args) do
    Mix.Task.run("app.start")
    
    {opts, _args, _invalid} = OptionParser.parse(args,
      switches: [
        analyze: :boolean,
        low_match: :boolean,
        run_id: :integer,
        threshold: :float,
        help: :boolean
      ],
      aliases: [
        a: :analyze,
        l: :low_match,
        r: :run_id,
        t: :threshold,
        h: :help
      ]
    )

    cond do
      opts[:help] ->
        show_help()

      opts[:analyze] ->
        ReprocessMatches.analyze_runs()

      opts[:run_id] ->
        run_id = opts[:run_id]
        IO.puts("Reprocessing reconciliation run #{run_id}...")
        
        case ReprocessMatches.reprocess_run(run_id) do
          {:ok, matches} ->
            IO.puts("✅ Successfully reprocessed run #{run_id} with #{length(matches)} matches")
          {:error, reason} ->
            IO.puts("❌ Failed to reprocess run #{run_id}: #{inspect(reason)}")
        end

      opts[:low_match] ->
        threshold = opts[:threshold] || 50.0
        IO.puts("Reprocessing all runs with match rate below #{threshold}%...")
        
        case ReprocessMatches.reprocess_low_match_runs(threshold) do
          {:ok, count} ->
            IO.puts("✅ Successfully reprocessed #{count} runs")
          {:error, reason} ->
            IO.puts("❌ Failed to reprocess runs: #{inspect(reason)}")
        end

      true ->
        IO.puts("No action specified. Use --help for usage information.")
        show_help()
    end
  end

  defp show_help do
    IO.puts("""
    Reconciliation Reprocessing Tool

    This tool helps reprocess existing reconciliation runs with improved matching logic.

    Usage:
      mix reconciliation.reprocess [options]

    Options:
      --analyze, -a              Analyze current reconciliation runs and show statistics
      --low-match, -l            Reprocess all runs with low match rates
      --run-id ID, -r ID         Reprocess a specific reconciliation run
      --threshold RATE, -t RATE  Set custom threshold for low match rate (default: 50.0)
      --help, -h                 Show this help message

    Examples:
      mix reconciliation.reprocess --analyze
      mix reconciliation.reprocess --low-match
      mix reconciliation.reprocess --run-id 123
      mix reconciliation.reprocess --low-match --threshold 75

    Note: This tool is useful after improving the matching algorithm to fix
    reconciliation runs that were processed with the old logic.
    """)
  end
end
